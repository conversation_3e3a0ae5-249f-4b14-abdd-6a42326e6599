<template>
  <div class="home">
    <!-- 主容器卡片 -->
    <div class="main-card">
      <!-- 标题区域 -->
      <div class="header-section"> 
          <img class="welcome-icon" src="@/assets/images/bid-examine/home-welcome.png" alt="欢迎图标">
          <span class="title-text">您好，欢迎使用采购文件合规性审查</span> 
      </div>

      <!-- 上传区域 -->
      <div class="upload-section">
        <upload-file
          ref="uploadFileRef"
          v-model:files="fileList"
          accept=".docx"
          accept-tip="文件格式不正确，请选择.docx文件"
          :show-upload-list="false"
          @change="doChange">
          <div class="upload-area">
            <img class="upload-icon" src="@/assets/images/bid-examine/upload-circle.png" alt="上传"> 
            <!-- 上传文字 -->
            <div class="upload-text-main">上传采购文件</div>
            <div class="upload-text-sub">仅支持 .docx 格式文档，单个文档大小不超过 20MB</div> 
            <div class="upload-text-hint">或将文件拖拽到此处</div>
          </div>
        </upload-file>
      </div>

      <!-- 文件列表区域 -->
      <div class="file-list-section">
        <!-- 文件列表标题和按钮 -->
        <div class="file-list-header">
          <div class="file-count-text" v-if="fileList.length > 0">已上传文件 ({{ fileList.filter(item=>item.status === 'done').length }}/{{ Math.max(0, fileList.length) }})</div>
          <div class="file-count-text" v-else></div>
          <div class="header-actions"> 
            <a-button type="primary" class="start-button" :class="{ disabled: !doneCount }" @click="handleStartReview">
              开始审查
            </a-button>
          </div>
        </div>

        <!-- 文件项列表 -->
        <div class="file-items-container">
          <div v-for="file in fileList" :key="file.uid" class="file-item" :class="`file-item-${file.status}`">
            <div class="file-close-btn" @click="handleRemoveFile(file)">
               <CloseOutlined :style="{'font-size': '8px'}"/>
            </div>
            <div class="file-detail">
              <svg-icon :icon="getFileIcon(file.name)" class="icon-file" /> 
              <div class="file-info">
                <div class="file-name" :title="file.name">{{ file.name }}</div>
                <div class="file-size">{{ file.size }}</div>
              </div>
            </div>
            <div class="file-status">
              <div :class="`status-${file.status}`">
                <component class="status-icon" :is="getStatusIcon(file.status).icon" />
                <span class="status-text">{{ getStatusIcon(file.status).text }}</span>
              </div>
              <a-button v-if="file.status === 'error'" class="retry-btn" type="link" @click="handleRetryUpload(file)">重试</a-button>
            </div>

            <!-- 进度条 -->
            <div  class="progress-bar">
              <div class="progress-fill" :class="file.status" :style="{ width: `${file.percent || 0}%` }"></div>
            </div> 
          </div>
        </div>
      </div>
      <!-- 最近添加的审查 -->
      <div class="recent-review-section">
        <div class="recent-review-header">
          <div class="recent-review-title">最近添加的审查</div>
          <a-button type="link" class="view-all-btn" @click="handleViewAll">
            查看完整列表
          </a-button>
        </div>

        <div class="recent-review-table">
          <a-table
            :columns="recentReviewColumns"
            :data-source="recentReviewList"
            :pagination="false"
            :loading="recentReviewLoading"
            :scroll="{ x: 'max-content' }"
            size="middle"
          >
            <!-- 项目信息列 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'projectInfo'">
                <div class="project-info">
                  <div class="project-name" :title="record.projectName">
                    {{ record.projectName || '-' }}
                  </div>
                  <div class="project-code" :title="record.projectCode">
                    {{ record.projectCode || '-' }}
                  </div>
                </div>
              </template>

              <!-- 审查结果列 -->
              <template v-else-if="column.dataIndex === 'reviewResult'">
                <div class="review-result">
                  <div class="result-progress">
                    <div class="progress-bar-mini">
                      <div
                        class="progress-fill-mini"
                        :class="getProgressClass(record.reviewResult)"
                        :style="{ width: getProgressWidth(record.reviewResult) }"
                      ></div>
                    </div>
                    <span class="progress-text">{{ getProgressText(record.reviewResult) }}</span>
                  </div>
                </div>
              </template>

              <!-- 创建人列 -->
              <template v-else-if="column.dataIndex === 'createUserName'">
                <span :title="record.createUserName">{{ record.createUserName || '-' }}</span>
              </template>

              <!-- 创建时间列 -->
              <template v-else-if="column.dataIndex === 'createTime'">
                <span :title="record.createTime">{{ formatDateTime(record.createTime) }}</span>
              </template>

              <!-- 操作列 -->
              <template v-else-if="column.dataIndex === 'actions'">
                <div class="action-buttons">
                  <a-button
                    type="link"
                    size="small"
                    @click="handleViewDetail(record)"
                    class="action-btn view-btn"
                  >
                    <svg-icon icon="icon-chakan" class="action-icon" />
                  </a-button>
                  <a-button
                    type="link"
                    size="small"
                    @click="handleDownload(record)"
                    class="action-btn download-btn"
                  >
                    <svg-icon icon="icon-xiazai" class="action-icon" />
                  </a-button>
                  <a-button
                    type="link"
                    size="small"
                    @click="handleDelete(record)"
                    class="action-btn delete-btn"
                  >
                    <svg-icon icon="icon-shanchu" class="action-icon" />
                  </a-button>
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang='ts'>
import { computed, ref, onMounted } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { apiTaskCreate, apiTaskList, apiTaskDelete } from '@/api/examine'
import { customDocumentBundleExport } from '@/api/download'
import { useRouter } from 'vue-router'
import { getFileIcon, getStatusIcon, getRiskStyle } from '@/views/hooks/examine'
import UploadFile from '@/components/UploadFile/index.vue'
import { message } from 'ant-design-vue'

interface FileItem {
  uid: string
  name: string
  size: number
  status: 'uploading' | 'done' | 'error'
  percent?: number
  response?: any
}
const router = useRouter()
const fileList = ref<FileItem[]>([])
const uploadFileRef = ref()

// 最近审查相关数据
const recentReviewList = ref<any[]>([])
const recentReviewLoading = ref(false)

// 表格列配置
const recentReviewColumns = [
  {
    title: '采购项目名称/编号',
    dataIndex: 'projectInfo',
    ellipsis: true,
    width: '35%'
  },
  {
    title: '文件名称',
    dataIndex: 'fileName',
    ellipsis: true,
    width: '20%'
  },
  {
    title: '审查结果',
    dataIndex: 'reviewResult',
    width: '15%',
    align: 'center'
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    width: '10%',
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: '15%',
    align: 'center'
  },
  {
    title: '操作',
    dataIndex: 'actions',
    width: '5%',
    align: 'center'
  }
]

function doChange(files:any) {
  fileList.value = [...files]
}

// 计算是否可以开始审查
const doneCount = computed(() => {
  return fileList.value.filter(file => file.status === 'done').length
}) 
// 删除文件
function handleRemoveFile(file: FileItem) {
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index !== -1) {
    fileList.value.splice(index, 1)
  }
}

// 重试上传
async function handleRetryUpload(file: FileItem) {
  await uploadFileRef.value?.handleRetry(file) 
}
const isStarting = ref(false)
async function handleStartReview() {
  if (!doneCount.value || isStarting.value) return
  const fileIdList = fileList.value.map((item: any) => item.response?.fileId)
  isStarting.value = true
  const {err,data} = await apiTaskCreate({fileIdList})
  isStarting.value = false
  if (err) return
  if(!data?.taskIdList?.length) {
    message.error('审查失败')
    return
  }
  router.push({
    name: 'ComplianceReview',
    query: { taskId:data.taskIdList[0] }
  })
}

// ==================== 最近审查相关方法 ====================

// 获取最近审查列表
async function getRecentReviewList() {
  recentReviewLoading.value = true
  try {
    const { data, err } = await apiTaskList({
      pageNum: 1,
      pageSize: 5 // 只显示最近5条
    })
    if (err) {
      console.error('获取最近审查列表失败:', err)
      return
    }
    recentReviewList.value = data?.dataList || []
  } catch (error) {
    console.error('获取最近审查列表异常:', error)
  } finally {
    recentReviewLoading.value = false
  }
}

// 查看完整列表
function handleViewAll() {
  router.push({ name: 'LibraryIndex' })
}

// 查看详情
function handleViewDetail(record: any) {
  router.push({
    name: 'ComplianceReview',
    query: { taskId: record.taskId }
  })
}

// 下载文件
async function handleDownload(record: any) {
  try {
    const params = {
      taskId: record.taskId,
      fileTypes: ['procurement_revised', 'risk_report']
    }
    await customDocumentBundleExport(params)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  }
}

// 删除任务
async function handleDelete(record: any) {
  try {
    const { err } = await apiTaskDelete({ taskId: record.taskId })
    if (err) {
      message.error('删除失败')
      return
    }
    message.success('删除成功')
    // 重新获取列表
    getRecentReviewList()
  } catch (error) {
    console.error('删除失败:', error)
    message.error('删除失败')
  }
}

// ==================== 工具函数 ====================

// 格式化日期时间
function formatDateTime(dateTime: string) {
  if (!dateTime) return '-'
  return dateTime.replace('T', ' ').substring(0, 16)
}

// 获取进度条样式类
function getProgressClass(reviewResult: number) {
  if (reviewResult === 0) return 'safe' // 未发现风险
  if (reviewResult === 1) return 'risk' // 发现风险
  return 'processing' // 其他状态
}

// 获取进度条宽度
function getProgressWidth(reviewResult: number) {
  if (reviewResult === 0) return '50%' // 未发现风险显示50%
  if (reviewResult === 1) return '50%' // 发现风险显示50%
  return '0%' // 其他状态
}

// 获取进度条文本
function getProgressText(reviewResult: number) {
  if (reviewResult === 0) return '50%'
  if (reviewResult === 1) return '50%'
  return '-'
}

// 页面初始化
onMounted(() => {
  getRecentReviewList()
})
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
  background-color: #F9FAFB;
  color: #000;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  box-sizing: border-box; 
  .main-card {     
    width: 1280px;
    background: #FFFFFF;
    border-radius: 16px;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    padding: 32px; 
  }

  .header-section {
    display: flex; 
    align-items: center; 
    justify-content: center;
    height: 48px;
    .welcome-icon {
      width: 48px;
      height: 48px;
      flex-shrink: 0;
      margin-right: 16px;
    }
    .title-text { 
      font-weight: 600;
      font-size: 30px; 
      color: #111827; 
    }
  }
:deep(.ant-upload-wrapper) {
    .ant-upload-select {
      width: 100%;
    }
  }
  .upload-section {
    width: 100%;
    margin-top: 32px;
  }

  .upload-area {
    width: 100%;
    height: 246px;
    background: #F7F8FA;
    border: 1px solid #E5E6EB;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;     
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-10px);
    } 
    .upload-icon {
      width: 64px;
      height: 64px;
      display: block;
    }

    .upload-text-main { 
      font-weight: 400;
      font-size: 20px;
      line-height: 1.4em;
      color: #111827;
      text-align: center;
      margin-top: 16px;
    }
    .upload-text-hint,
    .upload-text-sub{
      font-weight: 400; 
      margin-top: 12px;
    } 
    .upload-text-sub {  
      font-size: 16px; 
      color: #4B5563;
      text-align: center; 
    }

    .upload-text-hint { 
      color: #6B7280;
      font-size: 14px;
      line-height: 20px;
    }
  }

  // 文件列表区域
  .file-list-section {
    display: flex;
    flex-direction: column;  
    .file-list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin:32px 0 24px;
      .file-count-text { 
        font-weight: 600;
        font-size: 20px;
        line-height: 1.4em;
        color: #000000; 
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 16px;

        .test-button {
          padding: 8px 16px;
          height: 32px;
          background: #F0F5FF;
          border: 1px solid #8FB0FF;
          border-radius: 4px;
          font-family: 'Inter', sans-serif;
          font-weight: 400;
          font-size: 14px;
          color: #133CE8;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #E6EFFF;
            border-color: #3B66F5;
          }
        }

        .start-button {
          width: 138px;
          height: 48px;
          background: #133CE8;
          border-radius: 6px;
          border: none;
          font-family: 'Inter', sans-serif;
          font-weight: 600;
          font-size: 16px;
          line-height: 1.5em;
          color: #FFFFFF;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #3B66F5;
          }

          &:active {
            background: #0625C2;
          }

          &.disabled {
            background: #D1D5DB;
            color: #6B7280;
            cursor: not-allowed;
          }
        }
      }
    }

    .file-items-container {
      display: flex;
      flex-direction: row;
      gap: 16px;
      flex-wrap: wrap;
    }

    .file-item {
      position: relative;
      width: 394px;
      padding: 16px;
      border-radius: 8px;
      background: #FFFFFF;
      border: 1px solid #E5E6EB; 
      gap: 16px;  
      &.file-item-done {
        background: #F6FFED;
        border-color: #B7EB8F;
      }

      &.file-item-error {
        background: #FFF1F0;
        border-color: #FFA39E;
      }

      &.file-item-uploading {
        background: #F0F5FF;
        border-color: #8FB0FF;
      }

      .file-close-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        background: #FFFFFF;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
                    0px 3px 6px -4px rgba(0, 0, 0, 0.12),
                    0px 6px 16px 0px rgba(0, 0, 0, 0.08);
        color: #000000; 
      } 
      .file-detail {
        display: flex;
        align-items: center;
        width: 100%;
      }
      .icon-file {
        width: 32px;
        height: 32px;
        flex-shrink: 0;
        margin-right: 4px;
      }
      .file-info {
        flex: 1;
        min-width: 0; 

        .file-name {  
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis; 
          height: 22px;
          line-height: 22px;
          color: rgba(0, 0, 0, 0.88);
        } 
        .file-size { 
          color: rgba(0, 0, 0, 0.45);
        }
      }

      .file-status { 
        margin: 12px 0;
        display: flex;
        align-items: center;  
        justify-content: space-between;
        .status-done,
        .status-error,
        .status-uploading {
          display: flex;
          align-items: center;
          gap: 4px; 
        }
        .status-icon {
          width: 16px;
          height: 16px;
        }

        .status-done {
          color:var(--success-6);
        }

        .status-uploading {
          color: var(--main-6); 
        }

        .status-error {
          color: var(--error-6); 
        }
        .retry-btn {
          padding: 0;
          margin: 0;
          height: 22px;
          &:hover {
            color: #3B66F5;
          }
        }
      }

      .progress-bar { 
        height: 6px;
        background: #FFFFFF;
        border-radius: 6px;
        overflow: hidden; 
        .progress-fill {
          height: 100%;
          border-radius: 6px;  
          background: var(--main-6); 

          &.done {
            background: var(--success-6);
          }

          &.error {
            background: var(--error-6);
          }
        }
      }
    }
  }

  // 最近审查区域样式
  .recent-review-section {
    margin-top: 48px;

    .recent-review-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .recent-review-title {
        font-weight: 600;
        font-size: 20px;
        line-height: 1.4em;
        color: #000000;
      }

      .view-all-btn {
        padding: 0;
        height: auto;
        color: #133CE8;
        font-size: 14px;

        &:hover {
          color: #3B66F5;
        }
      }
    }

    .recent-review-table {
      background: #FFFFFF;
      border-radius: 8px;
      border: 1px solid #E5E6EB;
      overflow: hidden;

      :deep(.ant-table) {
        .ant-table-thead > tr > th {
          background: #FAFBFC;
          border-bottom: 1px solid #E5E6EB;
          font-weight: 500;
          color: #374151;
          padding: 12px 16px;
        }

        .ant-table-tbody > tr > td {
          padding: 16px;
          border-bottom: 1px solid #F3F4F6;
        }

        .ant-table-tbody > tr:hover > td {
          background: #F9FAFB;
        }
      }

      .project-info {
        .project-name {
          font-weight: 500;
          color: #111827;
          margin-bottom: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .project-code {
          font-size: 12px;
          color: #6B7280;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .review-result {
        .result-progress {
          display: flex;
          align-items: center;
          gap: 8px;

          .progress-bar-mini {
            width: 60px;
            height: 6px;
            background: #F3F4F6;
            border-radius: 3px;
            overflow: hidden;

            .progress-fill-mini {
              height: 100%;
              border-radius: 3px;
              transition: width 0.3s ease;

              &.safe {
                background: #10B981;
              }

              &.risk {
                background: #EF4444;
              }

              &.processing {
                background: #3B82F6;
              }
            }
          }

          .progress-text {
            font-size: 12px;
            color: #6B7280;
            min-width: 30px;
          }
        }
      }

      .action-buttons {
        display: flex;
        align-items: center;
        gap: 4px;

        .action-btn {
          padding: 4px;
          height: 24px;
          width: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;

          .action-icon {
            width: 14px;
            height: 14px;
          }

          &.view-btn {
            color: #3B82F6;

            &:hover {
              background: #EFF6FF;
              color: #1D4ED8;
            }
          }

          &.download-btn {
            color: #10B981;

            &:hover {
              background: #ECFDF5;
              color: #047857;
            }
          }

          &.delete-btn {
            color: #EF4444;

            &:hover {
              background: #FEF2F2;
              color: #DC2626;
            }
          }
        }
      }
    }
  }
}
</style>
